'use client'

import {
  useOrganizationsError,
  useOrganizations as useOrganizationsFromStore,
  useOrganizationsLoading,
  useSearchQuery,
  useSetError,
  useSetLoading,
  useSetOrganizations,
} from '@/lib/stores/organizationStore'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { buildQueryString } from '@/lib/utils/utils'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useMemo } from 'react'

interface UseOrganizationsOptions {
  enabled?: boolean
  refetchInterval?: number
}

/**
 * Custom hook to fetch organizations data and sync with Zustand store
 * This hook automatically updates the store when data changes
 */
export function useOrganizations(options: UseOrganizationsOptions = {}) {
  const { enabled = true, refetchInterval } = options

  // Get search query from store
  const searchQuery = useSearchQuery()

  // Get store actions
  const setOrganizations = useSetOrganizations()
  const setLoading = useSetLoading()
  const setError = useSetError()

  // Determine search config
  const searchConfig = useMemo(() => {
    return {
      query: searchQuery,
      filterType: 'name' as const,
    }
  }, [searchQuery])

  // Fetch organizations using React Query
  const {
    data: orgsData,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  } = useQuery<ApiResponseWithDataPaginationField<Organization>>({
    queryKey: ['getOrganizations', searchConfig.query, searchConfig.filterType, 0, 5, 'teams'],
    queryFn: () => {
      const requestParams = buildQueryString({
        filter: {
          search: searchConfig.query,
        },
        per_page: 5,
        page: 1,
      })

      const url = '/organizations?' + requestParams

      return queryFetchHelper(url)
    },
    enabled,
    refetchInterval,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  // Extract organizations from response
  const organizations = useMemo(() => orgsData?.data.data || [], [orgsData?.data.data])

  // Sync loading state with store
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  // Sync error state with store
  useEffect(() => {
    setError(isError)
  }, [isError, setError])

  // Sync organizations data with store
  useEffect(() => {
    if (organizations.length > 0) {
      setOrganizations(organizations)
    }
  }, [organizations, setOrganizations])

  return {
    organizations,
    isLoading: isLoading || isFetching,
    isError,
    error,
    refetch,
    isFetching,
  }
}

/**
 * Hook to get organizations data directly from store (without fetching)
 * Useful for components that only need to read data that's already been fetched
 */
export function useOrganizationsData() {
  const organizations = useOrganizationsFromStore()
  const isLoading = useOrganizationsLoading()
  const isError = useOrganizationsError()

  return {
    organizations,
    isLoading,
    isError,
  }
}

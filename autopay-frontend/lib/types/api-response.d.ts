interface ApiResponse<T = any> {
  message?: string
  success?: boolean
  data: T
}

type Link = {
  active: boolean
  label: string
  url: string
}

interface ApiResponseWithDataField<T = any> extends ApiResponse<T> {}
interface ApiResponseWithDataPaginationField<T = any> extends ApiResponse<Pagination<T>> {}

// Legacy type alias - use ApiResponseWithDataPaginationField instead
type ApiResponsePagination<T = any> = ApiResponseWithDataPaginationField<T>

import { Metadata } from 'next'

import PageHeading from '@/app/(panel)/common/components/page-heading'
import { SidebarNav } from '@/app/(panel)/settings/components/sidebar-nav'
import { Card, CardContent } from '@/components/ui/card'
import React from 'react'

export const metadata: Metadata = {
  title: 'Teams',
  description: 'Manage your teams and members.',
}

const sidebarNavItems = [
  {
    title: 'Thành viên',
    href: '/members',
  },
  {
    title: 'Teams',
    href: '/members/teams',
  },
  {
    title: 'Vai trò',
    href: '/members/roles',
  },
  {
    title: 'Thiế<PERSON> lập',
    href: '/members/settings',
  },
]

interface TeamsLayoutProps {
  children: React.ReactNode
}

export default function TeamsLayout({ children }: TeamsLayoutProps) {
  return (
    <>
      <PageHeading
        title="Thành viên & Teams"
        description="Quản lý teams và thành viên trong tổ chức của bạn."
        withSeparator={true}
      />
      <div className="flex flex-col space-y-8 lg:flex-row lg:space-y-0 lg:space-x-5">
        <aside className="md:-ml-1 md:w-52">
          <SidebarNav items={sidebarNavItems} />
        </aside>
        <Card className="flex-1">
          <CardContent className="px-6">{children}</CardContent>
        </Card>
      </div>
    </>
  )
}

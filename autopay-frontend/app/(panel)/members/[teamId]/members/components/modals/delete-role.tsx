import { useStore } from '@/app/(panel)/teams/[teamId]/roles/stores/store'
import { Icons } from '@/components/icons'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import NiceModal, { useModal } from '@ebay/nice-modal-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { useParams } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { useCopyToClipboard } from 'usehooks-ts'
import { z } from 'zod'

const FormSchema = z.object({
  name: z.string().min(1, {
    message: 'The name field is required.',
  }),
})

export default NiceModal.create(({ role }: { role: Role }) => {
  const modal = useModal()
  const { refetchTrigger } = useStore()

  const { teamId } = useParams<{ teamId: string }>()

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: '',
    },
  })

  const { isPending, mutate } = useMutation({
    mutationFn: (data: z.infer<typeof FormSchema>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/teams/${teamId}/roles`, {
        method: 'DELETE',
        body: JSON.stringify(data),
      }),
    onSuccess: (data) => {
      modal.remove()
      refetchTrigger()
      toast.success(data.message)
    },
    onError: (error: ApiResponse) => {
      toast.error(error.message)
    },
  })

  const [_, copy] = useCopyToClipboard()
  const copyText = (text: string) =>
    copy(text).then(() => {
      toast.success('Copied', {
        classNames: {
          toast: 'w-fit h-10',
        },
      })
    })

  return (
    <Dialog
      open={modal.visible}
      onOpenChange={(open) => (open ? modal.show() : modal.remove())}>
      <DialogContent className="max-w-sm rounded-xl md:max-w-xl">
        <DialogHeader>
          <DialogTitle>Delete Role</DialogTitle>
          <DialogDescription>This will revoke all permissions and delete the role from the team.</DialogDescription>
        </DialogHeader>
        <div>
          <Alert variant="destructive">
            <AlertDescription className="text-xs">
              <strong>Warning:</strong> This action is not reversible. Please be certain.
            </AlertDescription>
          </Alert>
        </div>
        <Form {...form}>
          <form onSubmit={form.handleSubmit((data) => mutate(data))}>
            <div className="my-4 space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Enter the role name
                      <span
                        className="mx-1 cursor-pointer font-bold"
                        onClick={() => copyText(role.name)}
                        data-tooltip-html="Copy">
                        {role.name}
                      </span>{' '}
                      to continue:
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        pattern={`\\s*${role.name}\\s*`}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="secondary">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={isPending}>
                {isPending && <Icons.spinner className="mr-1 h-4 w-4 animate-spin" />}
                Delete
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
})

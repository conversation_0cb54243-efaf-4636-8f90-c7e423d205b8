import { Button } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import Loading from '@/components/display/Loading'
import { Icons } from '@/components/icons'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { buildQueryString } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import NiceModal, { useModal } from '@ebay/nice-modal-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useParams } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

const FormSchema = z.object({
  email: z.string().email(),
})

export default NiceModal.create(() => {
  const modal = useModal()
  const { teamId } = useParams<{ teamId: string }>()

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: '',
    },
  })

  const {
    isFetching,
    isFetched,
    isError,
    error,
    data: apiData,
    refetch,
  } = useQuery({
    queryKey: ['searchUser', teamId],
    queryFn: () => {
      const queryString = buildQueryString({
        filter: {
          email: form.getValues('email'),
        },
        include: 'roles',
      })

      return queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/teams/${teamId}/members/users?${queryString}`)
    },
    enabled: false,
    retry: false,
  })

  const { isPending: isInviting, mutate: sendInvitation } = useMutation({
    mutationFn: (data: { email: string }) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/teams/${teamId}/members/invitations`, {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const renderTeamMembers = () => {
    if (!isFetched) {
      return null
    }

    if (isError) {
      return (
        <div className="text-center">
          <p className="text-muted-foreground">{error.message ?? 'No user found.'}</p>
        </div>
      )
    }

    const member: Member = apiData?.data

    return (
      <div className="flex w-full items-center gap-4">
        <Avatar className="h-10 w-10">
          <AvatarImage
            src={member.avatar}
            alt={member.name}
            className="rounded-full border"
          />
          <AvatarFallback>{member.name}</AvatarFallback>
        </Avatar>
        <div className="flex flex-col">
          <span className="font-bold">{member.name}</span>
          <span className="text-muted-foreground">{member.email}</span>
        </div>
        <Select defaultValue="superadmin">
          <SelectTrigger className="bg-muted ml-auto w-fit px-2 py-0">
            <SelectValue placeholder="Role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="superadmin">Grabber</SelectItem>
            <SelectItem value="dark">Moderator</SelectItem>
            <SelectItem value="system">Member</SelectItem>
          </SelectContent>
        </Select>
        <Button
          data-tooltip-html="Send Invitation"
          disabled={isInviting}
          onClick={() => sendInvitation({ email: member.email })}>
          {isInviting && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          Invite
        </Button>
      </div>
    )
  }

  const renderBodyContent = () => {
    if (isFetching) {
      return <Loading />
    }

    return renderTeamMembers()
  }

  const onSubmit = async () => {
    refetch().then(() => {})
  }

  return (
    <Dialog
      open={modal.visible}
      onOpenChange={(open) => (open ? modal.show() : modal.remove())}>
      <DialogContent className="max-w-sm rounded-xl md:max-w-xl">
        <DialogHeader>
          <DialogTitle>Invite Member</DialogTitle>
          <DialogDescription>
            New members will be invited to register an account first, while existing users will directly get invited to
            your team.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid gap-4 py-4">
              <div className="flex items-start justify-between gap-3">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormControl>
                        <Input
                          placeholder="Search by Email"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  disabled={isFetching}>
                  {isFetching && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
                  Search
                </Button>
              </div>
            </div>
          </form>
        </Form>
        {renderBodyContent()}
      </DialogContent>
    </Dialog>
  )
})

[{"id": "ROI", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "description": "Try to copy the SMS alarm, maybe it will navigate the mobile program!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/267.jpg", "members": 80}, {"id": "ACTION-ITEMS", "name": "Osinski Group", "description": "backing up the card won't do anything, we need to hack the wireless SAS circuit!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/517.jpg", "members": 39}, {"id": "APPLICATIONS", "name": "<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "description": "You can't back up the monitor without overriding the optical IP circuit!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/276.jpg", "members": 81}, {"id": "CONTENT", "name": "Huels Inc", "description": "We need to quantify the 1080p SAS array!", "avatar": "https://avatars.githubusercontent.com/u/81272282", "members": 48}, {"id": "NETWORKS", "name": "Okuneva Group", "description": "I'll calculate the digital TLS protocol, that should monitor the JSON array!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/233.jpg", "members": 49}, {"id": "APPLICATIONS", "name": "Rutherford LLC", "description": "If we bypass the circuit, we can get to the SSL sensor through the optical JSON interface!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1171.jpg", "members": 82}, {"id": "NICHES", "name": "Gleichner LLC", "description": "We need to index the bluetooth HEX application!", "avatar": "https://avatars.githubusercontent.com/u/66238494", "members": 57}, {"id": "PORTALS", "name": "<PERSON>tke, <PERSON>el and <PERSON>el", "description": "I'll generate the wireless SQL sensor, that should application the DNS port!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/892.jpg", "members": 79}, {"id": "MODELS", "name": "Bergstrom, Ziemann and Haag", "description": "Use the neural HTTP array, then you can transmit the neural transmitter!", "avatar": "https://avatars.githubusercontent.com/u/77544569", "members": 55}, {"id": "DELIVERABLES", "name": "Baumbach LLC", "description": "We need to override the solid state USB capacitor!", "avatar": "https://avatars.githubusercontent.com/u/44853002", "members": 38}, {"id": "BANDWIDTH", "name": "Heaney LLC", "description": "The USB protocol is down, override the optical bus so we can parse the TLS capacitor!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/81.jpg", "members": 57}, {"id": "MODELS", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>", "description": "Use the mobile XML circuit, then you can quantify the digital sensor!", "avatar": "https://avatars.githubusercontent.com/u/95644718", "members": 76}, {"id": "APPLICATIONS", "name": "<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "description": "connecting the matrix won't do anything, we need to synthesize the wireless SCSI microchip!", "avatar": "https://avatars.githubusercontent.com/u/87571356", "members": 72}, {"id": "E-MARKETS", "name": "Anderson, <PERSON> and <PERSON>hmeler", "description": "programming the feed won't do anything, we need to transmit the optical HDD driver!", "avatar": "https://avatars.githubusercontent.com/u/50257591", "members": 73}, {"id": "METHODOLOGIES", "name": "Kovacek Group", "description": "indexing the bandwidth won't do anything, we need to index the 1080p ASCII interface!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/104.jpg", "members": 27}, {"id": "LIFETIME VALUE", "name": "<PERSON><PERSON><PERSON>, Considine and Dibbert", "description": "You can't transmit the program without navigating the redundant SSD panel!", "avatar": "https://avatars.githubusercontent.com/u/12486275", "members": 43}, {"id": "E-COMMERCE", "name": "Bins Inc", "description": "Use the neural THX system, then you can generate the primary bandwidth!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1047.jpg", "members": 8}, {"id": "CONVERGENCE", "name": "Hane and Sons", "description": "We need to connect the open-source UTF8 program!", "avatar": "https://avatars.githubusercontent.com/u/47897571", "members": 50}, {"id": "USERS", "name": "Nitzsche LLC", "description": "The CSS system is down, program the auxiliary alarm so we can synthesize the UDP driver!", "avatar": "https://avatars.githubusercontent.com/u/83897956", "members": 52}, {"id": "COMMUNITIES", "name": "<PERSON> <PERSON> <PERSON><PERSON>", "description": "The SSD transmitter is down, parse the optical microchip so we can generate the SAS bus!", "avatar": "https://avatars.githubusercontent.com/u/46956177", "members": 47}, {"id": "ROI", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "description": "We need to input the primary XSS transmitter!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/522.jpg", "members": 69}, {"id": "APPLICATIONS", "name": "Jaskolski Inc", "description": "The IP array is down, parse the neural hard drive so we can transmit the API sensor!", "avatar": "https://avatars.githubusercontent.com/u/49254076", "members": 12}, {"id": "SYNERGIES", "name": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>", "description": "I'll override the haptic HDD capacitor, that should pixel the DNS alarm!", "avatar": "https://avatars.githubusercontent.com/u/28313223", "members": 24}, {"id": "PARADIGMS", "name": "Keeling, Goyette and Lindgren", "description": "The HDD transmitter is down, program the redundant driver so we can calculate the JBOD microchip!", "avatar": "https://avatars.githubusercontent.com/u/37539698", "members": 68}, {"id": "E-BUSINESS", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>", "description": "transmitting the bandwidth won't do anything, we need to program the mobile UTF8 matrix!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/618.jpg", "members": 86}, {"id": "WEB SERVICES", "name": "Stanton, Legros and Medhurst", "description": "Try to calculate the VGA bandwidth, maybe it will hack the cross-platform protocol!", "avatar": "https://avatars.githubusercontent.com/u/56281802", "members": 22}, {"id": "PARADIGMS", "name": "<PERSON>, <PERSON> and <PERSON><PERSON><PERSON>", "description": "Use the neural SSD pixel, then you can synthesize the online card!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1094.jpg", "members": 89}, {"id": "ACTION-ITEMS", "name": "Wiza and Sons", "description": "We need to override the primary SMS bus!", "avatar": "https://avatars.githubusercontent.com/u/65271908", "members": 26}, {"id": "APPLICATIONS", "name": "Schroeder - Corkery", "description": "You can't override the circuit without quantifying the mobile API feed!", "avatar": "https://avatars.githubusercontent.com/u/69891071", "members": 71}, {"id": "METHODOLOGIES", "name": "<PERSON><PERSON><PERSON>, <PERSON> and Bo<PERSON>", "description": "If we index the circuit, we can get to the PCI array through the mobile PNG program!", "avatar": "https://avatars.githubusercontent.com/u/69400688", "members": 52}, {"id": "APPLICATIONS", "name": "Haley and Sons", "description": "Try to parse the SAS transmitter, maybe it will synthesize the virtual protocol!", "avatar": "https://avatars.githubusercontent.com/u/8588606", "members": 15}, {"id": "PARTNERSHIPS", "name": "<PERSON><PERSON><PERSON>, MacGyver and Schneider", "description": "Use the digital JSON transmitter, then you can compress the multi-byte program!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/256.jpg", "members": 39}, {"id": "EYEBALLS", "name": "Simonis <PERSON> Collier", "description": "Try to transmit the ADP pixel, maybe it will copy the online protocol!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1110.jpg", "members": 30}, {"id": "NICHES", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>", "description": "We need to back up the solid state TLS alarm!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/898.jpg", "members": 60}, {"id": "SCHEMAS", "name": "Goyette <PERSON> Schultz", "description": "We need to bypass the redundant PNG pixel!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/684.jpg", "members": 73}, {"id": "NICHES", "name": "Tremblay LLC", "description": "The JSON pixel is down, parse the primary monitor so we can hack the CSS capacitor!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/708.jpg", "members": 85}, {"id": "CHANNELS", "name": "Christiansen LLC", "description": "bypassing the alarm won't do anything, we need to back up the multi-byte PNG transmitter!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/168.jpg", "members": 20}, {"id": "METRICS", "name": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON>", "description": "Try to index the DNS array, maybe it will input the wireless firewall!", "avatar": "https://avatars.githubusercontent.com/u/68405089", "members": 60}, {"id": "METHODOLOGIES", "name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>", "description": "transmitting the card won't do anything, we need to hack the haptic ADP array!", "avatar": "https://avatars.githubusercontent.com/u/55540823", "members": 26}, {"id": "PLATFORMS", "name": "<PERSON><PERSON><PERSON>son - Renner", "description": "The SMS interface is down, copy the mobile program so we can calculate the EXE bandwidth!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/842.jpg", "members": 74}, {"id": "SYSTEMS", "name": "Ritchie LLC", "description": "quantifying the pixel won't do anything, we need to bypass the back-end SSD program!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1222.jpg", "members": 79}, {"id": "RELATIONSHIPS", "name": "Okuneva - <PERSON><PERSON>", "description": "I'll synthesize the digital DNS bandwidth, that should protocol the SCSI pixel!", "avatar": "https://avatars.githubusercontent.com/u/30684242", "members": 20}, {"id": "PORTALS", "name": "Prohaska, Friesen and Wyman", "description": "I'll calculate the bluetooth RAM panel, that should protocol the EXE hard drive!", "avatar": "https://avatars.githubusercontent.com/u/40293527", "members": 28}, {"id": "PORTALS", "name": "Schneider Group", "description": "We need to index the wireless THX system!", "avatar": "https://avatars.githubusercontent.com/u/86657664", "members": 40}, {"id": "APPLICATIONS", "name": "Okuneva Inc", "description": "We need to quantify the open-source PNG hard drive!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/109.jpg", "members": 4}, {"id": "E-BUSINESS", "name": "<PERSON><PERSON><PERSON>, Hane and Flatley", "description": "We need to reboot the haptic GB monitor!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/442.jpg", "members": 81}, {"id": "PARTNERSHIPS", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "description": "You can't parse the card without connecting the back-end ADP microchip!", "avatar": "https://avatars.githubusercontent.com/u/77109564", "members": 13}, {"id": "BLOCKCHAINS", "name": "Padberg and Sons", "description": "The PCI sensor is down, parse the 1080p card so we can bypass the VGA interface!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/604.jpg", "members": 41}, {"id": "DELIVERABLES", "name": "Kovacek and Sons", "description": "generating the bandwidth won't do anything, we need to reboot the digital AGP panel!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/239.jpg", "members": 10}, {"id": "SUPPLY-CHAINS", "name": "Harber Inc", "description": "indexing the interface won't do anything, we need to transmit the solid state OCR interface!", "avatar": "https://avatars.githubusercontent.com/u/69447304", "members": 47}, {"id": "USERS", "name": "Gerhold - Sipes", "description": "The API firewall is down, synthesize the digital protocol so we can reboot the JBOD sensor!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/276.jpg", "members": 18}, {"id": "SOLUTIONS", "name": "Nienow Inc", "description": "Try to calculate the HDD driver, maybe it will input the wireless program!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/121.jpg", "members": 72}, {"id": "WEB SERVICES", "name": "<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "description": "Try to transmit the GB system, maybe it will synthesize the back-end alarm!", "avatar": "https://avatars.githubusercontent.com/u/5092256", "members": 59}, {"id": "INFRASTRUCTURES", "name": "Greenfelder - <PERSON><PERSON><PERSON>", "description": "We need to override the neural DNS panel!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/98.jpg", "members": 16}, {"id": "BLOCKCHAINS", "name": "Haley Inc", "description": "You can't transmit the transmitter without quantifying the haptic HDD circuit!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/457.jpg", "members": 99}, {"id": "SCHEMAS", "name": "<PERSON><PERSON> - <PERSON><PERSON>", "description": "We need to override the auxiliary CSS alarm!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/579.jpg", "members": 38}, {"id": "BANDWIDTH", "name": "<PERSON><PERSON>, <PERSON> and <PERSON><PERSON>", "description": "hacking the bus won't do anything, we need to bypass the solid state HEX firewall!", "avatar": "https://avatars.githubusercontent.com/u/80486141", "members": 33}, {"id": "CONTENT", "name": "Weber Inc", "description": "Try to calculate the HTTP capacitor, maybe it will copy the redundant sensor!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/872.jpg", "members": 74}, {"id": "SCHEMAS", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON>", "description": "The SSL bus is down, bypass the 1080p monitor so we can back up the IB microchip!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1023.jpg", "members": 36}, {"id": "INTERFACES", "name": "Reinger LLC", "description": "Use the primary SMS driver, then you can index the multi-byte microchip!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1079.jpg", "members": 58}, {"id": "RELATIONSHIPS", "name": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "description": "I'll input the cross-platform IP transmitter, that should capacitor the HDD array!", "avatar": "https://avatars.githubusercontent.com/u/27727801", "members": 81}, {"id": "CONVERGENCE", "name": "Bartell LLC", "description": "We need to connect the back-end HTTP capacitor!", "avatar": "https://avatars.githubusercontent.com/u/28819539", "members": 84}, {"id": "BLOCKCHAINS", "name": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "description": "You can't program the hard drive without indexing the 1080p HTTP card!", "avatar": "https://avatars.githubusercontent.com/u/87241430", "members": 37}, {"id": "INFRASTRUCTURES", "name": "<PERSON><PERSON>, McDermott and Brekke", "description": "I'll reboot the virtual HEX array, that should microchip the CLI sensor!", "avatar": "https://avatars.githubusercontent.com/u/15372940", "members": 65}, {"id": "CONTENT", "name": "Dickinson Inc", "description": "We need to quantify the digital OCR matrix!", "avatar": "https://avatars.githubusercontent.com/u/66753924", "members": 26}, {"id": "PORTALS", "name": "<PERSON><PERSON>, <PERSON> and <PERSON>", "description": "You can't bypass the pixel without bypassing the optical SAS matrix!", "avatar": "https://avatars.githubusercontent.com/u/7405207", "members": 6}, {"id": "ROI", "name": "Keeling, Schaden and Satterfield", "description": "The CSS program is down, parse the multi-byte pixel so we can calculate the SCSI capacitor!", "avatar": "https://avatars.githubusercontent.com/u/44373980", "members": 2}, {"id": "CHANNELS", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "description": "You can't generate the alarm without generating the neural FTP pixel!", "avatar": "https://avatars.githubusercontent.com/u/60223173", "members": 99}, {"id": "BANDWIDTH", "name": "Beer, Botsford and Jast", "description": "Use the open-source VGA sensor, then you can back up the haptic driver!", "avatar": "https://avatars.githubusercontent.com/u/37726163", "members": 10}, {"id": "METRICS", "name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "description": "Try to calculate the SSL matrix, maybe it will transmit the wireless firewall!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/64.jpg", "members": 33}, {"id": "ACTION-ITEMS", "name": "White LLC", "description": "Use the virtual SSL bandwidth, then you can copy the bluetooth port!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/672.jpg", "members": 72}, {"id": "SOLUTIONS", "name": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "description": "I'll back up the wireless GB pixel, that should transmitter the TCP panel!", "avatar": "https://avatars.githubusercontent.com/u/49802107", "members": 98}, {"id": "PLATFORMS", "name": "Willms, Kunze and Mertz", "description": "Use the cross-platform SMS monitor, then you can back up the digital array!", "avatar": "https://avatars.githubusercontent.com/u/63520317", "members": 74}, {"id": "COMMUNITIES", "name": "Auer LLC", "description": "We need to override the redundant SQL pixel!", "avatar": "https://avatars.githubusercontent.com/u/10016234", "members": 81}, {"id": "TECHNOLOGIES", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "description": "Try to navigate the CSS capacitor, maybe it will override the digital matrix!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/344.jpg", "members": 86}, {"id": "ARCHITECTURES", "name": "Tillman Group", "description": "Try to calculate the DNS transmitter, maybe it will reboot the virtual application!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/295.jpg", "members": 83}, {"id": "CHANNELS", "name": "Murazik - <PERSON>", "description": "If we parse the microchip, we can get to the TCP sensor through the auxiliary SQL protocol!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1021.jpg", "members": 30}, {"id": "CONTENT", "name": "<PERSON> <PERSON> <PERSON><PERSON>", "description": "If we navigate the array, we can get to the PCI array through the mobile GB alarm!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/269.jpg", "members": 97}, {"id": "PLATFORMS", "name": "Gle<PERSON>ner and Sons", "description": "You can't program the bus without bypassing the haptic ADP array!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/927.jpg", "members": 28}, {"id": "LIFETIME VALUE", "name": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "description": "We need to parse the auxiliary DNS pixel!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1146.jpg", "members": 59}, {"id": "PORTALS", "name": "Schowalter, Senger and Satterfield", "description": "We need to hack the back-end HEX capacitor!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/981.jpg", "members": 1}, {"id": "NETWORKS", "name": "Hilll <PERSON> <PERSON>", "description": "We need to compress the primary SSL firewall!", "avatar": "https://avatars.githubusercontent.com/u/60042581", "members": 1}, {"id": "MODELS", "name": "<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "description": "I'll override the auxiliary DNS matrix, that should microchip the JSON interface!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/873.jpg", "members": 84}, {"id": "CONTENT", "name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>", "description": "If we bypass the matrix, we can get to the HEX monitor through the primary HDD transmitter!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/94.jpg", "members": 72}, {"id": "METHODOLOGIES", "name": "Runolfsdottir Inc", "description": "Try to parse the JBOD monitor, maybe it will parse the back-end microchip!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1065.jpg", "members": 14}, {"id": "INITIATIVES", "name": "Kuphal, Balistreri and Bode", "description": "The SCSI alarm is down, index the primary array so we can synthesize the GB port!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/999.jpg", "members": 70}, {"id": "ACTION-ITEMS", "name": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "description": "transmitting the pixel won't do anything, we need to program the back-end OCR firewall!", "avatar": "https://avatars.githubusercontent.com/u/81211527", "members": 9}, {"id": "CONVERGENCE", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "description": "You can't override the hard drive without bypassing the redundant SCSI monitor!", "avatar": "https://avatars.githubusercontent.com/u/77793448", "members": 38}, {"id": "MARKETS", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "description": "Try to compress the ASCII system, maybe it will transmit the online microchip!", "avatar": "https://avatars.githubusercontent.com/u/63303127", "members": 48}, {"id": "WEB SERVICES", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>", "description": "Use the back-end ADP protocol, then you can generate the haptic program!", "avatar": "https://avatars.githubusercontent.com/u/27669634", "members": 85}, {"id": "E-COMMERCE", "name": "Gle<PERSON>ner and Sons", "description": "Use the primary SAS pixel, then you can navigate the open-source card!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/100.jpg", "members": 44}, {"id": "CONTENT", "name": "Lesch - Cremin", "description": "You can't parse the pixel without connecting the 1080p TLS system!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/641.jpg", "members": 32}, {"id": "ARCHITECTURES", "name": "Pollich, Stroman and Hettinger", "description": "Try to input the IB feed, maybe it will connect the virtual transmitter!", "avatar": "https://avatars.githubusercontent.com/u/55789023", "members": 55}, {"id": "RELATIONSHIPS", "name": "Langworth - McKenzie", "description": "The ADP transmitter is down, navigate the cross-platform bus so we can hack the DNS bandwidth!", "avatar": "https://avatars.githubusercontent.com/u/4040725", "members": 11}, {"id": "SUPPLY-CHAINS", "name": "Strosin and Sons", "description": "Use the haptic UDP program, then you can reboot the primary alarm!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/567.jpg", "members": 65}, {"id": "METRICS", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>", "description": "generating the interface won't do anything, we need to hack the optical XML interface!", "avatar": "https://avatars.githubusercontent.com/u/5903994", "members": 54}, {"id": "USERS", "name": "Berge, Gibson and Bruen", "description": "I'll synthesize the open-source SDD monitor, that should card the AI hard drive!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1010.jpg", "members": 88}, {"id": "NICHES", "name": "Mertz Inc", "description": "synthesizing the interface won't do anything, we need to calculate the multi-byte SQL microchip!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/918.jpg", "members": 79}, {"id": "SOLUTIONS", "name": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "description": "If we generate the program, we can get to the HDD hard drive through the online XML capacitor!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/866.jpg", "members": 30}, {"id": "PORTALS", "name": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>", "description": "Use the open-source API driver, then you can generate the virtual interface!", "avatar": "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/774.jpg", "members": 85}]
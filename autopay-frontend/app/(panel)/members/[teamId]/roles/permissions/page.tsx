'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import React from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';

const PermissionSchema = z.object({
  name: z.string().nonempty({ message: 'Permission name is required' }),
  description: z.string().optional(),
});

const AbilitySchema = z.object({
  name: z.string().nonempty({ message: 'Ability name is required' }),
  description: z.string().optional(),
  permissions: z
    .array(PermissionSchema)
    .min(1, { message: 'At least one permission is required' }),
});

const AbilitiesSchema = z.array(AbilitySchema);

type AbilitiesFormValues = z.infer<typeof AbilitiesSchema>;

const abilities = [
  {
    name: 'Dashboard',
    description: 'Server Dashboard',
    permissions: [
      {
        name: 'View server dashboard data',
      },
    ],
  },
  {
    name: 'Health',
    description: 'Health Monitoring',
    permissions: [
      {
        name: 'View server health page',
      },
      {
        name: 'Run disk cleanup',
      },
    ],
  },
  {
    name: 'Web Application',
    description: 'Site Management',
    permissions: [
      {
        name: 'View web application page',
      },
      {
        name: 'Create web application',
      },
      {
        name: 'Rebuild web application',
      },
      {
        name: 'Set/Unset default web application',
      },
      {
        name: 'Change web application settings',
      },
      {
        name: 'Delete web application',
      },
      {
        name: 'View web server log',
      },
      {
        name: 'Create web application alias',
      },
      {
        name: 'Attach git repository',
      },
      {
        name: 'View data transfer page',
      },
      {
        name: 'Change git branch',
      },
      {
        name: 'Manage git deployment script',
        description: 'Create, update, delete, and view git deployment script',
      },
      {
        name: 'Detach git repository',
        description:
          'Create, edit, rename, change permissions and edit your files inside web application(s)',
      },
      {
        name: 'Add domain name',
      },
      {
        name: 'Delete domain name',
      },
      {
        name: 'Manage domain name',
      },
      {
        name: 'Install SSL/TLS Certificate',
        description: "Install Let's Encrypt or custom certificate",
      },
      {
        name: 'Manage SSL/TLS certificate',
        description: "Update and/or redeploy (Let's Encrypt) SSL/TLS settings",
      },
      {
        name: 'Delete SSL/TLS certificate',
      },
      {
        name: 'Allow to run chown tool',
      },
      {
        name: 'Allow to change web application owner',
      },
      {
        name: 'Allow to clone web application',
      },
      {
        name: 'Allow to set web application basic auth',
      },
      {
        name: 'Manage NGINX custom config',
      },
      {
        name: 'Manage web application firewall',
      },
      {
        name: 'Manage WordPress staging site',
      },
    ],
  },
  {
    name: 'Database',
    description: 'Database Management',
    permissions: [
      {
        name: 'View database page',
      },
      {
        name: 'Create database',
      },
      {
        name: 'Delete database',
      },
      {
        name: 'Add database user',
      },
      {
        name: 'Change database user password',
      },
      {
        name: 'Delete database user',
      },
      {
        name: 'Assign database user to database',
      },
      {
        name: 'Revoke database user from database',
      },
    ],
  },
  {
    name: 'Supervisor',
    description: 'Process Management',
    permissions: [
      {
        name: 'View supervisor page',
      },
      {
        name: 'Add supervisor job',
      },
      {
        name: 'Reload supervisor job',
      },
      {
        name: 'Update supervisor job',
      },
      {
        name: 'Rebuild supervisor job',
      },
      {
        name: 'Delete supervisor job',
      },
    ],
  },
  {
    name: 'Cron Job',
    description: 'Scheduled Task Management',
    permissions: [
      {
        name: 'View cron job page',
      },
      {
        name: 'Add cron job',
      },
      {
        name: 'Edit cron job',
      },
      {
        name: 'Rebuild cron job',
      },
      {
        name: 'Delete cron job',
      },
    ],
  },
  {
    name: 'PHP CLI',
    description: 'PHP Command Line Interface',
    permissions: [
      {
        name: 'View PHP CLI page',
      },
      {
        name: 'Update PHP CLI',
      },
    ],
  },
  {
    name: 'Deployment Key',
    description: 'Deployment Key Management',
    permissions: [
      {
        name: 'View deployment key page',
      },
      {
        name: 'Manage deployment key',
        description: 'Generate and regenerate deployment key',
      },
    ],
  },
  {
    name: 'Notification',
    description: 'Notification Management',
    permissions: [
      {
        name: 'View notification page',
      },
      {
        name: 'Manage notification',
        description: 'Add, update, delete Slack/Telegram notification',
      },
      {
        name: 'Manage health notification',
      },
    ],
  },
  {
    name: 'Log',
    description: 'Log Management',
    permissions: [
      {
        name: 'View log page',
      },
    ],
  },
  {
    name: 'Services',
    description: 'Service Management',
    permissions: [
      {
        name: 'View services page',
      },
      {
        name: 'Manage services',
        description: 'Start, stop, reload services',
      },
    ],
  },
  {
    name: 'Security',
    description: 'Security Management',
    permissions: [
      {
        name: 'View security page',
      },
      {
        name: 'Add firewall rules',
      },
      {
        name: 'Deploy firewall rules',
      },
      {
        name: 'Delete firewall rules',
      },
      {
        name: 'Kill SSH session',
      },
      {
        name: 'Manual ban IP addresses',
      },
      {
        name: 'Delete Fail2Ban blocked IP address',
      },
    ],
  },
  {
    name: 'System User',
    description: 'System User Management',
    permissions: [
      {
        name: 'View system user page',
      },
      {
        name: 'Add system user',
      },
      {
        name: "Change system user's password",
      },
      {
        name: "Change system user's setting",
      },
      {
        name: 'Delete system user',
      },
    ],
  },
  {
    name: 'SSH Key',
    description: 'SSH Key Management',
    permissions: [
      {
        name: 'View ssh key page',
      },
      {
        name: 'Add SSH key',
      },
      {
        name: 'Delete SSH key',
      },
    ],
  },
];

export default function Component() {
  const form = useForm<AbilitiesFormValues>({
    resolver: zodResolver(AbilitiesSchema),
    defaultValues: abilities,
  });

  function onSubmit(data: AbilitiesFormValues) {
    toast({
      title: 'You submitted the following values:',
      description: (
        <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify(data, null, 2)}</code>
        </pre>
      ),
    });
  }

  return (
    <div className="max-w-3xl">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardContent className="mt-4 flex flex-col gap-10">
              {abilities.map((ability, abilityIndex) => (
                <React.Fragment key={ability.name}>
                  <FormItem className="flex flex-row items-start space-y-0">
                    <div className="w-1/3">
                      <FormLabel className="text-base">
                        {ability.name}
                      </FormLabel>
                      {ability.description && (
                        <FormDescription className="text-[0.8rem]">
                          {ability.description}
                        </FormDescription>
                      )}
                    </div>
                    <div className="flex flex-col gap-3">
                      {ability.permissions.map(
                        (permission, permissionIndex) => (
                          <FormField
                            key={permission.name}
                            control={form.control}
                            name={`${abilityIndex}.name`}
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={true}
                                    onCheckedChange={() => {}}
                                  />
                                </FormControl>
                                <FormLabel>{permission.name}</FormLabel>
                              </FormItem>
                            )}
                          />
                        ),
                      )}
                      <FormMessage />
                    </div>
                  </FormItem>
                  {abilityIndex < abilities.length - 1 && <Separator />}
                </React.Fragment>
              ))}
            </CardContent>
            <CardFooter className="pt-auto flex items-center justify-between rounded-b-xl bg-muted px-6 py-3">
              <span className="text-xs text-muted-foreground">
                These permissions will be applied to each team members with this
                role.
              </span>
              <Button type="submit">Save Permission</Button>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  );
}

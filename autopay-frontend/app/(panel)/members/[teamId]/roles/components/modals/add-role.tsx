import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

import { Icons } from '@/components/icons'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import NiceModal, { useModal } from '@ebay/nice-modal-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { useParams } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

const FormSchema = z.object({
  name: z.string().min(1, {
    message: 'The name field is required.',
  }),
  description: z.string().optional(),
})

export default NiceModal.create(({ callback }: { callback: () => void }) => {
  const modal = useModal()
  const { teamId } = useParams<{ teamId: string }>()

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  })

  const { isPending, mutate } = useMutation({
    mutationFn: (data: z.infer<typeof FormSchema>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/teams/${teamId}/roles`, {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onSuccess: (data) => {
      callback()
      modal.remove()
      toast.success(data.message)
    },
    onError: (error: ApiResponse) => {
      toast.error(error.message)
    },
  })

  return (
    <Dialog
      open={modal.visible}
      onOpenChange={(open) => (open ? modal.show() : modal.remove())}>
      <DialogContent className="max-w-sm rounded-xl md:max-w-xl">
        <DialogHeader>
          <DialogTitle>Create a Role</DialogTitle>
          <DialogDescription>Create a role and add members to collaborate on your team role.</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit((data) => mutate(data))}>
            <div className="my-4 space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="secondary">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={isPending}>
                {isPending && <Icons.spinner className="mr-1 h-4 w-4 animate-spin" />}
                Create
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
})

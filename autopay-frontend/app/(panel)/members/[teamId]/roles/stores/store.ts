import { create } from 'zustand';
import { nanoid } from 'nanoid';
import { PaginationState } from '@tanstack/table-core';

type Store = {
  pagination: PaginationState;
  refetchFlag: string; // for modal actions
  refetchTrigger: () => void;
  setPagination: (
    updater: PaginationState | ((state: PaginationState) => PaginationState),
  ) => void;
};

export const useStore = create<Store>((set) => ({
  pagination: {
    pageIndex: 0,
    pageSize: 10,
  },
  refetchFlag: nanoid(),
  refetchTrigger: () => set((state) => ({ refetchFlag: nanoid() })),
  setPagination: (updater) =>
    set((state) => ({
      pagination:
        typeof updater === 'function' ? updater(state.pagination) : updater,
    })),
}));

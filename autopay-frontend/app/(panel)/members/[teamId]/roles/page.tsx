'use client'

import EmptyState from '@/assets/vectors/empty-states/09.svg'
import EmptyStateNoRecord from '@/assets/vectors/empty-states/13.svg'
import ButtonCustom from '@/components/custom-ui/button-custom'
import Loading from '@/components/display/Loading'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import { use, useRef, useState } from 'react'
import { FaExclamationCircle } from 'react-icons/fa'
import { FiPlusCircle } from 'react-icons/fi'
import { useDebounceCallback } from 'usehooks-ts'

import AddMemberModal from '@/app/(panel)/teams/[teamId]/members/components/modals/add-member'
import AddRoleModal from '@/app/(panel)/teams/[teamId]/roles/components/modals/add-role'
import DeleteRoleModal from '@/app/(panel)/teams/[teamId]/roles/components/modals/delete-role'
import { useStore } from '@/app/(panel)/teams/[teamId]/roles/stores/store'
import { buildQueryString } from '@/lib/utils'
import NiceModal from '@ebay/nice-modal-react'

NiceModal.register('addTeamRole', AddRoleModal)
NiceModal.register('deleteTeamRole', DeleteRoleModal)
NiceModal.register('addTeamRoleMember', AddMemberModal)

const RowLayout = dynamic(() => import('@/app/(panel)/teams/[teamId]/roles/displays/row'), {
  ssr: true,
  loading: () => <Loading />,
})

type Props = Promise<{
  teamId: string
}>

export default function Component({ params }: { params: Props }) {
  const { teamId } = use(params)
  const { refetchFlag, refetchTrigger, pagination } = useStore()

  const searchRef = useRef<HTMLInputElement>(null)
  const [search, setSearch] = useState('')
  const debounced = useDebounceCallback(setSearch, 500)
  const clearSearch = () => {
    if (searchRef.current) {
      searchRef.current.value = ''
    }
    setSearch('')
  }

  const { isPending, error, data } = useQuery({
    queryKey: ['getRoles', refetchFlag, search, pagination],
    queryFn: async (): Promise<ApiResponseWithDataPaginationField> => {
      const queryString = buildQueryString({
        filter: {
          name: search,
        },
        include: 'members',
        page: pagination.pageIndex + 1 + '',
        per_page: pagination.pageSize + '',
      })

      return await queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/teams/${teamId}/roles?` + queryString)
    },
  })

  const showModal = (name: string) => {
    NiceModal.show(name, {
      callback: () => {
        clearSearch()
        refetchTrigger()
      },
    }).then()
  }

  const renderBodyContent = () => {
    if (isPending) {
      return <Loading />
    }

    if (error) {
      return (
        <Alert variant="destructive">
          <FaExclamationCircle className="h-4 w-4" />
          <AlertTitle>Error!</AlertTitle>
          <AlertDescription className="text-xs">We were unable to fetch data, please try again later.</AlertDescription>
        </Alert>
      )
    }

    if (!data?.data?.data?.length && !search) {
      return (
        <div className="flex min-h-96 flex-col items-center justify-center gap-6 text-center">
          <Image
            src={EmptyState}
            alt="Teams"
            width={300}
            priority={true}
          />
          <div>
            There is no role.
            <br />
            Create your first role now
          </div>
          <Button
            variant="outline"
            size="lg"
            onClick={() => showModal('addTeamRole')}>
            <FiPlusCircle className="mr-2 h-4 w-4" />
            Add Role
          </Button>
        </div>
      )
    }

    if (!data?.data?.data?.length) {
      return (
        <div className="flex min-h-96 flex-col items-center justify-center gap-6 text-center">
          <Image
            src={EmptyStateNoRecord}
            alt="No Role"
            width={300}
            priority={true}
          />
          <div className="flex max-w-sm flex-col gap-2">
            <strong>No role found.</strong>
            <p>
              Your search &quot;{search}&quot; did not match any roles.
              <br />
              Please try again.
            </p>
          </div>
          <div className="flex gap-4">
            <Button
              variant="outline"
              size="lg"
              onClick={() => clearSearch()}>
              Clear search
            </Button>
            <Button
              size="lg"
              onClick={() => showModal('addTeamRole')}>
              <FiPlusCircle className="mr-2 h-4 w-4" />
              New Role
            </Button>
          </div>
        </div>
      )
    }

    return <RowLayout apiData={data} />
  }

  return (
    <>
      <div className="mb-4 flex items-center justify-between space-x-2">
        <Input
          ref={searchRef}
          type="search"
          placeholder="Search..."
          className="w-[200px]"
          defaultValue={search}
          onChange={(event) => debounced(event.target.value)}
        />
        <ButtonCustom
          md="icon"
          lg="default"
          onClick={() => showModal('addTeamRole')}>
          <FiPlusCircle className="h-4 w-4 md:mr-2" />
          <span className="hidden md:block">Add Role</span>
        </ButtonCustom>
      </div>

      {renderBodyContent()}
    </>
  )
}

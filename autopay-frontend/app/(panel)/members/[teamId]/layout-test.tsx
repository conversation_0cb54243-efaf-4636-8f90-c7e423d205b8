import { Separator } from '@/components/ui/separator';
import { SidebarNav } from '@/app/(panel)/teams/[teamId]/common/components/sidebar-nav';
import Layout from '@/app/(panel)/teams/[teamId]/common/layouts/default';

import React from 'react';
import { Metadata } from 'next';

interface SettingsLayoutProps {
  children: React.ReactNode;
  params: {
    teamId: string;
  };
}

export const metadata: Metadata = {
  title: 'Team Settings',
  description: 'Manage your shared servers with roles and permissions.',
};

export default function Component({
  children,
  params: { teamId },
}: SettingsLayoutProps) {
  const prefixRoute = `/teams/${teamId}`;
  const sidebarNavItems = [
    {
      title: 'Servers',
      href: `${prefixRoute}/servers`,
    },
    {
      title: 'Members',
      href: `${prefixRoute}/members`,
    },
    {
      title: 'Roles & Permissions',
      href: `${prefixRoute}/roles`,
    },
    {
      title: 'Settings',
      href: prefixRoute,
    },
  ];

  return (
    <Layout teamId={teamId}>
      <Separator className="my-6" />
      <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
        <aside className="md:-mx-3 lg:w-1/5">
          <SidebarNav items={sidebarNavItems} />
        </aside>
        <div className="flex-1">{children}</div>
      </div>
    </Layout>
  );
}

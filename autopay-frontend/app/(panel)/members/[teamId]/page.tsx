'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useFieldArray, useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CopyIcon } from '@radix-ui/react-icons';

const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, {
      message: 'Username must be at least 2 characters.',
    })
    .max(30, {
      message: 'Username must not be longer than 30 characters.',
    }),
  email: z
    .string({
      required_error: 'Please select an email to display.',
    })
    .email(),
  bio: z.string().max(160).min(4),
  urls: z
    .array(
      z.object({
        value: z.string().url({ message: 'Please enter a valid URL.' }),
      }),
    )
    .optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

// This can come from your database or API.
const defaultValues: Partial<ProfileFormValues> = {
  bio: 'I own a computer.',
  urls: [
    { value: 'https://shadcn.com' },
    { value: 'http://twitter.com/shadcn' },
  ],
};

export default function Component() {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues,
    mode: 'onChange',
  });

  const { fields, append } = useFieldArray({
    name: 'urls',
    control: form.control,
  });

  function onSubmit(data: ProfileFormValues) {
    toast({
      title: 'You submitted the following values:',
      description: (
        <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify(data, null, 2)}</code>
        </pre>
      ),
    });
  }

  return (
    <div className="max-w-3xl">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Team Name</CardTitle>
                  <CardDescription>
                    This is your team&apos;s visible name within RunWP. For
                    example, the name of your company or department.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FormItem>
                    <FormControl>
                      <Input placeholder="CODETAY" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </CardContent>
                <CardFooter className="pt-auto bg-muted flex items-center justify-between rounded-b-xl px-6 py-3">
                  <span className="text-muted-foreground text-xs">
                    Please use 32 characters at maximum.
                  </span>
                  <Button>Save</Button>
                </CardFooter>
              </Card>
            )}
          />
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <Card>
                <CardHeader className="flex flex-row justify-between">
                  <div>
                    <CardTitle className="text-lg">Team Avatar</CardTitle>
                    <CardDescription className="mt-2">
                      This is your Team Avatar. <br />
                      Click on the avatar to upload a custom one from your
                      files.
                    </CardDescription>
                  </div>
                  <Avatar className="h-20 w-20">
                    <AvatarImage
                      src="https://www.gravatar.com/avatar/f0adb5bdcd4cd8f697dfd9fe95c9e5f4"
                      alt="@codetay"
                    />
                    <AvatarFallback>CT</AvatarFallback>
                  </Avatar>
                </CardHeader>
                <CardContent></CardContent>
                <CardFooter className="pt-auto bg-muted flex items-center rounded-b-xl px-6 py-3">
                  <span className="text-muted-foreground text-xs">
                    An avatar is optional but strongly recommended.
                  </span>
                </CardFooter>
              </Card>
            )}
          />
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Team ID</CardTitle>
                  <CardDescription>
                    This is your Team ID within RunWP.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FormItem>
                    <FormControl>
                      <div className="flex">
                        <Input
                          placeholder="s44eu33cdsxxcdf5"
                          {...field}
                          readOnly
                        />
                        <Button
                          variant="link"
                          className="-ml-12 no-underline"
                          data-tooltip-html="Copy"
                        >
                          <CopyIcon />
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </CardContent>
              </Card>
            )}
          />
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Leave Team</CardTitle>
                  <CardDescription>
                    Revoke your access to this Team. Any resources you&apos;ve
                    added to the Team will remain.
                  </CardDescription>
                </CardHeader>
                <CardFooter className="pt-auto dark:bg-destructive dark:text-destructive-foreground flex items-center justify-between rounded-b-xl bg-rose-50 px-6 py-3">
                  <Button
                    variant="destructive"
                    className="dark:bg-primary dark:text-primary-foreground ml-auto"
                  >
                    Leave Team
                  </Button>
                </CardFooter>
              </Card>
            )}
          />
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Delete Team</CardTitle>
                  <CardDescription>
                    Permanently remove your Team and all of its contents from
                    the RunWP platform. This action is not reversible — please
                    continue with caution.
                  </CardDescription>
                </CardHeader>
                <CardFooter className="pt-auto dark:bg-destructive flex items-center justify-between rounded-b-xl bg-rose-50 px-6 py-3">
                  <span className="text-xs">
                    To delete your account, visit{' '}
                    <Link
                      href="/account"
                      className="text-primary font-semibold"
                    >
                      account settings
                    </Link>
                  </span>
                  <Button
                    variant="destructive"
                    className="dark:bg-primary dark:text-primary-foreground ml-auto"
                  >
                    Delete Team
                  </Button>
                </CardFooter>
              </Card>
            )}
          />
        </form>
      </Form>
    </div>
  );
}

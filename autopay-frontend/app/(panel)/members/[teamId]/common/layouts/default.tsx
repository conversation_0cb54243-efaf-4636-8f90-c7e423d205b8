'use client'

import Loading from '@/components/display/Loading'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import React, { useEffect } from 'react'
import { toast } from 'sonner'

type Props = React.PropsWithChildren<{
  teamId: string
  className?: string
  topRight?: React.ReactNode
}>

export default function Component({ teamId, topRight, children, className }: Props) {
  const router = useRouter()
  const queryKey = ['getTeam', teamId]

  const {
    isPending,
    error,
    data: apiData,
  } = useQuery({
    queryKey,
    queryFn: (): Promise<ApiResponseTeam> => queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + '/teams/' + teamId),
  })

  useEffect(() => {
    if (error) {
      toast.error(error.message)

      router.push('/teams')
    }
  }, [error, router])

  const renderBodyContent = () => {
    if (isPending) {
      return <Loading />
    }

    if (error) {
      return null
    }

    const {
      data: { name, description },
    } = apiData

    return (
      <div>
        <h2 className="text-2xl font-bold tracking-tight">{name}</h2>
        <p className="text-muted-foreground">{description}</p>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="flex flex-col justify-between space-y-2 md:flex-row md:items-center">{topRight}</div>
      {renderBodyContent()}
      {!isPending && !error && children}
    </div>
  )
}

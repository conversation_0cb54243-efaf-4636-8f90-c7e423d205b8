'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { cn } from '@/lib/utils';
import { buttonVariants } from '@/components/ui/button';
import { useCallback } from 'react';

interface SidebarNavProps extends React.HTMLAttributes<HTMLElement> {
  items: {
    href: string;
    title: string;
  }[];
}

export function SidebarNav({ className, items, ...props }: SidebarNavProps) {
  const pathname = usePathname();

  const checkStartWithPath = (path: string, href: string) => {
    let hrefs = href.split('/');

    // skip href length 3 (in the main page): ['', 'teams', 'CODETAY']
    if (hrefs.length < 4) return false;

    // path length 5: ['', 'teams', 'CODETAY', 'roles', 'permissions']
    return path.startsWith(href);
  };

  return (
    <nav
      className={cn(
        'flex space-x-0 overflow-x-auto lg:flex-col lg:space-y-1',
        className,
      )}
      {...props}
    >
      {items.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            'hover:bg-muted',
            buttonVariants({ variant: 'ghost' }),
            item.href === pathname || checkStartWithPath(pathname, item.href)
              ? 'font-black'
              : 'font-normal',
            'justify-start px-3',
          )}
        >
          {item.title}
        </Link>
      ))}
    </nav>
  );
}

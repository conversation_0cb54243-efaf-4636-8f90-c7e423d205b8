import { z } from 'zod'

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.
export const dataSchema = z.object({
  id: z.string(),
  name: z.string(),
  alias: z.string(),
  description: z.string().optional().nullable(),
  owner_id: z.string(),
  organization_id: z.string().optional().nullable(),
  is_owner: z.boolean().optional().nullable(),
  is_default_team: z.boolean().optional().nullable(),
  users_count: z.number().optional().nullable(),
  created_at: z.string(),
})

export type Data = z.infer<typeof dataSchema>

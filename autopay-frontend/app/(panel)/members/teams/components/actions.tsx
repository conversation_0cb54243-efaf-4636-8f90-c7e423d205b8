import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import NiceModal from '@ebay/nice-modal-react'
import Link from 'next/link'
import React from 'react'
import { FaPlus, FaTrash, FaUser<PERSON>, FaWrench } from 'react-icons/fa'
import { FaServer } from 'react-icons/fa6'
import { RiVipCrown2Line } from 'react-icons/ri'

export function Actions({ triggerChildren, team }: { triggerChildren: React.ReactNode; team: Team }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{triggerChildren}</DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => NiceModal.show('addTeamMemberIndex', { team })}>
          <FaPlus className="mr-2 h-4 w-4" />
          Thêm thành viên
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/teams/${team.id}/servers`}>
            <FaServer className="mr-2 h-4 w-4" />
            Servers
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/teams/${team.id}/members`}>
            <FaUsers className="mr-2 h-4 w-4" />
            Thành viên
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/teams/${team.id}/roles`}>
            <RiVipCrown2Line className="mr-2 h-4 w-4" />
            Vai trò
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href={`/teams/${team.id}`}>
            <FaWrench className="mr-2 h-4 w-4" />
            Cài đặt
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />

        <DropdownMenuItem
          className="focus:bg-destructive focus:text-white"
          onClick={() => NiceModal.show('deleteTeam', { team })}>
          <FaTrash className="mr-2 h-4 w-4" />
          Xóa
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

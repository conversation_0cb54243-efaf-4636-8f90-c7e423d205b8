import { Button } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { CopyIcon } from '@radix-ui/react-icons'
import { GrRefresh } from 'react-icons/gr'

import Loading from '@/components/display/Loading'
import { Icons } from '@/components/icons'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { buildQueryString } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import NiceModal, { useModal } from '@ebay/nice-modal-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { useCopyToClipboard } from 'usehooks-ts'

export default NiceModal.create(({ team }: { team: Team }) => {
  const modal = useModal()

  const queryKey = ['getTeam', team]
  const queryClient = useQueryClient()

  const queryString = buildQueryString({
    include: 'creator,members',
  })

  const { isPending, data: apiData } = useQuery({
    queryKey,
    queryFn: (): Promise<ApiResponse<Team>> =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/teams/${team.id}?${queryString}`),
  })

  const { isPending: patching, mutate: mutatePatching } = useMutation({
    mutationFn: (data: PatchTeamRequest) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + '/teams/' + team.id, {
        method: 'PATCH',
        body: JSON.stringify(data),
      }),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.setQueryData(queryKey, response)
        return toast.success(response.message, {
          classNames: {
            toast: 'w-fit h-10',
          },
        })
      }

      toast.error(response.message)
    },
  })

  const [_, copy] = useCopyToClipboard()
  const inviteLink = process.env.NEXT_PUBLIC_APP_URL + '/teams/invite/' + apiData?.data?.invitation_link
  const copyText = () => {
    if (!inviteLink) {
      return
    }

    copy(inviteLink).then(() => {
      toast.success('Copied', {
        classNames: {
          toast: 'w-fit h-10',
        },
      })
    })
  }

  const renderTeamMembers = () => {
    let members: Member[]

    if (!apiData || !apiData.data.members) {
      return (
        <div className="text-center">
          <p className="text-muted-foreground">No member yet</p>
        </div>
      )
    }

    members = apiData.data.members.filter((member: Member) => member.id !== apiData.data.owner_id)

    if (members.length > 5) {
      members = members.slice(0, 5)
    }

    return members.map((member) => {
      if (member.id === apiData.data.owner_id) return null

      return (
        <div
          className="flex w-full items-center gap-4"
          key={member.id}>
          <Avatar className="h-10 w-10">
            <AvatarImage
              src={member.avatar}
              alt={member.name}
              className="rounded-full border"
            />
            <AvatarFallback>{member.name}</AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="font-bold">{member.name}</span>
            <span className="text-muted-foreground">{member.email}</span>
          </div>
          <Select defaultValue="superadmin">
            <SelectTrigger className="bg-muted ml-auto w-fit px-2 py-0">
              <SelectValue placeholder="Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="superadmin">Grabber</SelectItem>
              <SelectItem value="dark">Moderator</SelectItem>
              <SelectItem value="system">Member</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )
    })
  }

  const renderBodyContent = () => {
    if (isPending) {
      return <Loading />
    }

    return (
      <>
        <div className="grid gap-6">
          <div className="font-semibold">People with access</div>
          {renderTeamMembers()}
          <div className="flex w-full items-center gap-4">
            <Avatar className="h-10 w-10">
              <AvatarImage
                src={apiData?.data?.creator?.avatar}
                alt="CODETAY"
                className="rounded-full border"
              />
              <AvatarFallback>{apiData?.data?.creator?.name}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-bold">{apiData?.data?.creator?.name}</span>
              <span className="text-muted-foreground">{apiData?.data?.creator?.email}</span>
            </div>
            <div className="text-muted-foreground ml-auto italic">Owner</div>
          </div>
          <div className="font-semibold">Invitation Link</div>
          {/* TODO: Implement invitation link feature in backend */}
          {/* <div className="flex w-full items-center gap-4">
            <Switch
              disabled={patching}
              id="invitation_link_enabled"
              checked={apiData?.data.invitation_link_enabled}
              onCheckedChange={(status) => mutatePatching({ invitation_link_enabled: status })}
            />
            <div className="flex flex-col">
              <div className="flex items-center space-x-2">
                <Label
                  htmlFor="invitation_link_enabled"
                  className="font-bold">
                  Anyone with the link
                </Label>
              </div>
              <span className="text-muted-foreground">can join your team</span>
            </div>
            <Icons.spinner className={`ml-auto h-4 w-4 ${patching ? 'animate-spin' : 'invisible'}`} />
          </div>
          {apiData?.data.invitation_link_enabled && (
            <div className="flex">
              <Input
                value={inviteLink}
                readOnly
                className="bg-muted"
                onClick={() => copyText()}
              />
              <div className="absolute right-7 flex gap-1">
                <Button
                  variant="link"
                  size="icon"
                  className="w-6"
                  onClick={() => copyText()}>
                  <CopyIcon />
                </Button>
                <Button
                  variant="link"
                  size="icon"
                  className="w-6"
                  onClick={() => mutatePatching({ reset_invitation_link: true })}>
                  <GrRefresh />
                </Button>
              </div>
            </div>
          )} */}
        </div>
      </>
    )
  }

  return (
    <Dialog
      open={modal.visible}
      onOpenChange={(open) => (open ? modal.show() : modal.remove())}>
      <DialogContent className="max-w-sm rounded-xl md:max-w-xl">
        <DialogHeader>
          <DialogTitle>Invite Team Member</DialogTitle>
          <DialogDescription>
            New members will be invited to register an account first, while existing users will directly get invited to
            your team.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex w-full items-center justify-between gap-3">
            <Input
              id="name"
              placeholder="Search by Email or Username"
              onChange={() => {}}
            />
            <Button>Invite</Button>
          </div>
        </div>
        {renderBodyContent()}
      </DialogContent>
    </Dialog>
  )
})

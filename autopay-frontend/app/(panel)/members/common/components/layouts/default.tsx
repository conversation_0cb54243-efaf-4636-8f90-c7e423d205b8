'use client';

import React from 'react';
import { cn } from '@/lib/utils';
type Props = React.PropsWithChildren<{
  title?: string;
  description?: string;
  className?: string;
  topRight?: React.ReactNode;
}>;

export default function DashboardLayout({
  title,
  description,
  topRight,
  children,
  className,
}: Props) {
  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex flex-col justify-between space-y-2 md:flex-row md:items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{title}</h2>
          <p className="text-muted-foreground">{description}</p>
        </div>
        {topRight}
      </div>
      {children}
    </div>
  );
}

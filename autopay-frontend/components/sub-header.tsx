import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils/utils'
import Link from 'next/link'
import * as React from 'react'

import Lot<PERSON> from '@/components/custom-ui/lottie'

export enum ItemType {
  ANIMATED = 'animated',
  LINK = 'link',
  TEXT_LINK = 'text-link',
  ICON = 'icon',
  IMAGE = 'image',
  TEXT = 'text',
  INPUT = 'input',
  BUTTON = 'button',
}

export enum IconType {
  ICON = 'icon',
  ANIMATED = 'animated',
  COMPONENT = 'component',
}

export interface Icon {
  type: IconType
  data?: any
  class?: string
}

export interface Item {
  type: ItemType
  data?: any
  class?: string
  href?: string
  icon?: Icon
  placeholder?: string
  tippy?: string
  action?: () => void
  container?: {
    class?: string
  }
  emit?: string
  model?: string
}

type SubHeaderData = {
  left: Item[]
  right: Item[]
}

interface SubHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  data: SubHeaderData
  className?: string
  showInServerRoute?: boolean
  [key: string]: any // Allow dynamic event handlers for emit functionality
}

export function SubHeader({ data, className, showInServerRoute = false, ...props }: SubHeaderProps) {
  const [showTerminalModal, setShowTerminalModal] = React.useState(false)

  const handleToggleTerminalModal = (e: React.MouseEvent) => {
    e.preventDefault()
    setShowTerminalModal(!showTerminalModal)
  }

  const renderItem = (item: Item, index: number) => {
    switch (item.type) {
      case ItemType.ANIMATED:
        return (
          <div
            key={index}
            className={cn('size-10', item.class)}>
            {typeof window !== 'undefined' && Lottie && item.data?.animationData && (
              <Lottie
                animationData={item.data.animationData}
                className="size-full"
              />
            )}
          </div>
        )

      case ItemType.LINK:
        return (
          <Link
            key={index}
            href={item.href || '#'}
            className={item.class}>
            {item.icon && renderIcon(item.icon)}
            {item.data?.class ? <span className={item.data.class}>{item.data.text}</span> : item.data}
          </Link>
        )

      case ItemType.TEXT_LINK:
        return (
          <Link
            key={index}
            href={item.href || '#'}
            className={item.class}>
            {item.data}
          </Link>
        )

      case ItemType.ICON:
        return (
          <div
            key={index}
            className={item.class}>
            {item.icon && renderIcon(item.icon)}
          </div>
        )

      case ItemType.IMAGE:
        return (
          <div
            key={index}
            className="mr-5 flex flex-wrap">
            <img
              src={item.data}
              alt=""
              className={item.class}
            />
          </div>
        )

      case ItemType.TEXT:
        return (
          <div
            key={index}
            className="text-muted-foreground flex flex-wrap font-bold uppercase">
            {item.data}
          </div>
        )

      case ItemType.INPUT:
        return (
          <div
            key={index}
            className={item.container?.class}>
            <Input
              placeholder={item.placeholder}
              className={item.class}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && item.emit) {
                  // Call dynamic event handler if it exists
                  const eventHandler = props[`on${item.emit}`]
                  if (typeof eventHandler === 'function') {
                    eventHandler((e.target as HTMLInputElement).value)
                  }
                }
              }}
            />
            {item.icon && renderIcon(item.icon)}
          </div>
        )

      case ItemType.BUTTON:
        return (
          <Button
            key={index}
            variant="outline"
            className={item.class}
            onClick={item.action}
            // TODO: Add tooltip using shadcn/ui tooltip
          >
            {item.icon && renderIcon(item.icon)}
            {item.data}
          </Button>
        )

      default:
        return null
    }
  }

  const renderIcon = (icon: Icon) => {
    if (!icon) return null

    switch (icon.type) {
      case IconType.ANIMATED:
        return (
          <div className={icon.class}>
            {typeof window !== 'undefined' && Lottie && icon.data?.animationData && (
              <Lottie
                animationData={icon.data.animationData}
                className="size-full"
              />
            )}
          </div>
        )

      case IconType.ICON:
      case IconType.COMPONENT:
        const IconComponent = icon.data
        return IconComponent ? <IconComponent className={icon.class} /> : null

      default:
        return <i className={icon.class} />
    }
  }

  return (
    <div {...props}>
      <div className={cn('flex items-center justify-between gap-2 md:h-16', className)}>
        <div className="flex w-fit items-center justify-start gap-1">
          {data.left.map((item, index) => renderItem(item, index))}
        </div>

        <div className="flex items-center justify-between gap-2">
          {data.right.map((item, index) => renderItem(item, index))}

          {showInServerRoute && (
            <Button
              variant="ghost"
              size="icon"
              className="hidden px-0 outline-none md:flex"
              onClick={handleToggleTerminalModal}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="size-10 fill-black dark:fill-white">
                <path
                  fillRule="evenodd"
                  d="M14.447 3.027a.75.75 0 01.527.92l-4.5 16.5a.75.75 0 01-1.448-.394l4.5-16.5a.75.75 0 01.921-.526zM16.72 6.22a.75.75 0 011.06 0l5.25 5.25a.75.75 0 010 1.06l-5.25 5.25a.75.75 0 11-1.06-1.06L21.44 12l-4.72-4.72a.75.75 0 010-1.06zm-9.44 0a.75.75 0 010 1.06L2.56 12l4.72 4.72a.75.75 0 11-1.06 1.06L.97 12.53a.75.75 0 010-1.06l5.25-5.25a.75.75 0 011.06 0z"
                  clipRule="evenodd"
                />
              </svg>
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
